'use client';

import React from 'react';
import { FaGraduationCap } from 'react-icons/fa';

const Education = () => {
  const educationData = [
    {
      year: "2016-2018",
      degree: "Matric",
      institution: "Royal Grammar School"
    },
    {
      year: "2018-2020", 
      degree: "Fsc Pre-Engineering",
      institution: "DPS & C"
    },
    {
      year: "2020-2024",
      degree: "Bachelor in Software Engineering", 
      institution: "GCUF"
    }
  ];

  return (
    <section className="min-h-screen flex flex-col lg:flex-row items-center" style={{backgroundColor: 'rgb(16, 19, 20)'}}>
      {/* Left Side - Education Content */}
      <div className="w-full lg:w-1/2 flex flex-col justify-center p-4 md:p-8 lg:p-16 text-white">
        {/* Main Title */}
        <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-8 md:mb-12 text-orange-500">
          Education
        </h2>

        {/* Education Timeline */}
        <div className="space-y-6 md:space-y-8">
          {educationData.map((item, index) => (
            <div 
              key={index}
              className="bg-orange-500 rounded-2xl p-6 md:p-8 relative hover:scale-105 transition-transform duration-300"
            >
              {/* Icon */}
              <div className="absolute -left-3 top-6 w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center border-4 border-gray-800">
                <FaGraduationCap className="text-white text-xl" />
              </div>
              
              {/* Content */}
              <div className="ml-6">
                {/* Year */}
                <div className="text-white font-bold text-lg md:text-xl mb-2">
                  {item.year}
                </div>
                
                {/* Degree */}
                <div className="text-white font-semibold text-base md:text-lg mb-1">
                  {item.degree}
                </div>
                
                {/* Institution */}
                <div className="text-white/90 text-sm md:text-base">
                  {item.institution}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Right Side - Education Illustration */}
      <div className="w-full lg:w-1/2 flex justify-center items-center p-4 md:p-8 lg:p-16">
        <div className="relative">
          <div className="w-64 h-64 md:w-80 md:h-80 lg:w-96 lg:h-[600px] relative">
            {/* Education Illustration SVG */}
            <svg 
              viewBox="0 0 400 400" 
              className="w-full h-full"
              fill="none"
            >
              {/* Background Circle */}
              <circle cx="200" cy="300" r="80" fill="#ff6b35" opacity="0.8" />
              
              {/* Laptop/Screen */}
              <rect x="120" y="150" width="160" height="120" rx="12" fill="#ff6b35" />
              <rect x="130" y="160" width="140" height="90" rx="8" fill="rgb(16, 19, 20)" />
              
              {/* Code on screen */}
              <text x="200" y="190" textAnchor="middle" fill="#ff6b35" fontSize="20" fontFamily="monospace" fontWeight="bold">
                &lt;/&gt;
              </text>
              
              {/* Code lines */}
              <rect x="140" y="200" width="60" height="3" fill="#ff6b35" opacity="0.7" />
              <rect x="140" y="210" width="80" height="3" fill="#ff6b35" opacity="0.7" />
              <rect x="140" y="220" width="50" height="3" fill="#ff6b35" opacity="0.7" />
              <rect x="140" y="230" width="70" height="3" fill="#ff6b35" opacity="0.7" />
              
              {/* Laptop base */}
              <rect x="130" y="250" width="140" height="8" rx="4" fill="#ff6b35" />
              
              {/* Graduation cap */}
              <g transform="translate(280, 120)">
                {/* Cap base */}
                <ellipse cx="0" cy="0" rx="40" ry="8" fill="#ff6b35" />
                {/* Cap top */}
                <rect x="-35" y="-15" width="70" height="15" rx="7" fill="#ff6b35" />
                {/* Tassel */}
                <line x1="35" y1="-7" x2="50" y2="10" stroke="#ff6b35" strokeWidth="3" />
                <circle cx="50" cy="10" r="3" fill="#ff6b35" />
              </g>
              
              {/* Books stack */}
              <g transform="translate(80, 280)">
                <rect x="0" y="0" width="60" height="8" fill="#ff6b35" opacity="0.9" />
                <rect x="5" y="-8" width="60" height="8" fill="#ff6b35" opacity="0.7" />
                <rect x="10" y="-16" width="60" height="8" fill="#ff6b35" opacity="0.5" />
              </g>
              
              {/* Floating elements */}
              <circle cx="100" cy="100" r="4" fill="#ff6b35" opacity="0.6" />
              <circle cx="320" cy="80" r="3" fill="#ff6b35" opacity="0.4" />
              <circle cx="80" cy="180" r="5" fill="#ff6b35" opacity="0.5" />
              <circle cx="340" cy="200" r="4" fill="#ff6b35" opacity="0.3" />
            </svg>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Education;
