'use client';

import React, { useEffect, useRef } from 'react';
import { FaGraduationCap } from 'react-icons/fa';
import { gsap } from 'gsap';

const Education = () => {
  const imageRef = useRef<HTMLImageElement>(null);

  const educationData = [
    {
      year: "2016-2018",
      degree: "Matric",
      institution: "Royal Grammar School"
    },
    {
      year: "2018-2020",
      degree: "Fsc Pre-Engineering",
      institution: "DPS & C"
    },
    {
      year: "2020-2024",
      degree: "Bachelor in Software Engineering",
      institution: "GCUF"
    }
  ];

  useEffect(() => {
    if (imageRef.current) {
      // GSAP bounce animation
      gsap.to(imageRef.current, {
        y: -20,
        duration: 1,
        ease: "power2.inOut",
        yoyo: true,
        repeat: -1
      });
    }
  }, []);

  return (
    <section className="min-h-screen flex flex-col lg:flex-row items-center" style={{backgroundColor: 'rgb(16, 19, 20)'}}>
      {/* Left Side - Education Content */}
      <div className="w-full lg:w-1/2 flex flex-col justify-center p-4 md:p-8 lg:p-16 text-white">
        {/* Main Title */}
        <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-8 md:mb-12 text-orange-500">
          Education
        </h2>

        {/* Education Timeline */}
        <div className="space-y-6 md:space-y-8">
          {educationData.map((item, index) => (
            <div
              key={index}
              className="bg-orange-500 rounded-2xl p-6 md:p-8 relative hover:scale-105 transition-transform duration-300 flex items-center"
            >
              {/* Icon */}
              <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center mr-6 flex-shrink-0">
                <FaGraduationCap className="text-orange-500 text-xl" />
              </div>

              {/* Content */}
              <div className="flex-1">
                {/* Year */}
                <div className="text-white font-bold text-lg md:text-xl mb-2">
                  {item.year}
                </div>

                {/* Degree */}
                <div className="text-white font-semibold text-base md:text-lg mb-1">
                  {item.degree}
                </div>

                {/* Institution */}
                <div className="text-white/90 text-sm md:text-base">
                  {item.institution}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Right Side - Education Image */}
      <div className="w-full lg:w-1/2 flex justify-center items-center p-4 md:p-8 lg:p-16 h-full min-h-screen">
        <div className="relative flex items-center justify-center h-full">
          <div className="w-64 h-64 md:w-80 md:h-80 lg:w-[500px] lg:h-[600px] relative">
            <img
              ref={imageRef}
              src="/educationimage-Tt59OOcC.png"
              alt="Education illustration"
              className="w-full h-full object-contain"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default Education;
