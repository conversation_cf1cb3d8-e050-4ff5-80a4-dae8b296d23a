'use client';

import React from 'react';
import { FaBriefcase, FaCalendarAlt } from 'react-icons/fa';

const Experience = () => {
  const experiences = [
    {
      title: "Instructor",
      company: "Innvoytech",
      period: "09/2024 – 11/2025",
      skills: "MER<PERSON> Stack, React.js, Next.js, Teaching"
    },
    {
      title: "UI Developer",
      company: "Crective",
      period: "02/2025 – 03/2025",
      skills: "React.js, Next.js, UI/UX Design, API Integration"
    },
    {
      title: "UI Designer/Frontend React JS Dev",
      company: "Genius Mind Zone",
      period: "12/2024 – 01/2025",
      skills: "React.js, UI Design, Responsive Design"
    },
    {
      title: "Frontend Developer",
      company: "Pak Freelancer Software House",
      period: "07/2023 – 01/2024",
      skills: "HTML5, CSS3, JavaScript, Responsive Design"
    }
  ];

  return (
    <section className="min-h-screen flex flex-col justify-center py-16" style={{backgroundColor: 'rgb(16, 19, 20)'}}>
      <div className="container mx-auto px-4 md:px-8 lg:px-16">
        {/* Title */}
        <div className="flex items-center mb-12 md:mb-16">
          <FaBriefcase className="text-red-500 text-3xl mr-4" />
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white">
            Experience
          </h2>
        </div>

        {/* Experience List */}
        <div className="max-w-2xl">
          <div className="relative">
            {/* Timeline Line */}
            <div className="absolute left-4 w-0.5 h-full bg-red-500"></div>

            {experiences.map((exp, index) => (
              <div key={index} className="relative mb-8">
                {/* Timeline Dot */}
                <div className="absolute left-2 w-4 h-4 bg-red-500 rounded-full border-4 border-gray-900 z-10"></div>

                {/* Content */}
                <div className="ml-12">
                  <h3 className="text-xl md:text-2xl font-bold text-white mb-1">
                    {exp.title}
                  </h3>
                  <h4 className="text-gray-300 font-medium mb-2">
                    {exp.company}
                  </h4>
                  <p className="text-gray-400 text-sm mb-2">
                    {exp.skills}
                  </p>
                  <div className="flex items-center text-red-500 text-sm">
                    <FaCalendarAlt className="mr-2" />
                    <span>{exp.period}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Experience;
