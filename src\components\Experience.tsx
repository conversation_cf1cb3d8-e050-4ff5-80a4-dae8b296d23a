'use client';

import React from 'react';

const Experience = () => {
  const experiences = [
    {
      title: "Instructor",
      company: "Innvoytech",
      period: "09/2024 – 11/2025",
      location: "Faisalabad, Pakistan",
      description: "As a dedicated instructor at InnvoyTech, teach different forms of web development to the trainee developers to become comfortable and confident with web-related skills. My areas of interest in teaching is MERN Stack Development, along with creating hands-on projects directly related to real-world applications.",
      current: true
    },
    {
      title: "UI Developer",
      company: "Crective",
      period: "02/2025 – 03/2025",
      location: "DHA Phase 8, Pakistan",
      description: "At Crective, I worked as a React JS/Next Developer where I contributed to the design and development of various projects. I focused on UI/UX design, implemented functional API integrations, and building responsive, user friendly interfaces.",
      projects: [
        "CRM Platform: https://crm-crective-demo.netlify.app",
        "AutoPublish Tool: https://autopublish-crective.vercel.app",
        "Crypto Bot Interface: https://crypto-bot-front-end.vercel.app/",
        "Email Responder System: https://emailresponder.crective.tech",
        "Marketplace App: https://crective-marketplace.online",
        "German Guest Post: https://germanguestpost.com/en"
      ]
    },
    {
      title: "UI Designer/Frontend React JS Dev",
      company: "Genius Mind Zone",
      period: "12/2024 – 01/2025",
      location: "Faisalabad, Pakistan",
      description: "At GMZ, I contributed to the Social Downloader project (https://dashbone.social/) by designing the YouTube section, which includes profile views, a video short downloader, and ensuring overall website responsiveness. I also worked on Complya, an AI-powered compliance platform (https://complya.com/), currently under development, where I designed various screens, including model screens, and implemented responsive layouts to improve the overall user experience."
    },
    {
      title: "Frontend Developer",
      company: "Pak Freelancer Software House",
      period: "07/2023 – 01/2024",
      location: "Faisalabad, Pakistan",
      description: "At Pak Freelancer Software House, Wapda City, FSD, I spent 6 months developing responsive and user-friendly web applications using HTML5, CSS3, and JavaScript. By applying responsive design principles, I ensured consistent user experiences across various devices."
    }
  ];

  return (
    <section className="min-h-screen flex flex-col justify-center py-16" style={{backgroundColor: 'rgb(16, 19, 20)'}}>
      <div className="container mx-auto px-4 md:px-8 lg:px-16">
        {/* Title */}
        <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-center mb-12 md:mb-16 text-orange-500">
          Experience
        </h2>

        {/* Experience Timeline */}
        <div className="max-w-4xl mx-auto">
          <div className="relative">
            {/* Timeline Line */}
            <div className="absolute left-4 md:left-1/2 transform md:-translate-x-1/2 w-0.5 h-full bg-orange-500"></div>

            {experiences.map((exp, index) => (
              <div key={index} className={`relative flex items-center mb-12 ${index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'}`}>
                {/* Timeline Dot */}
                <div className="absolute left-4 md:left-1/2 transform md:-translate-x-1/2 w-4 h-4 bg-orange-500 rounded-full border-4 border-gray-900 z-10"></div>

                {/* Content */}
                <div className={`ml-12 md:ml-0 md:w-1/2 ${index % 2 === 0 ? 'md:pr-8' : 'md:pl-8'}`}>
                  <div className="bg-gray-900 p-6 rounded-lg border border-orange-500 hover:shadow-lg hover:shadow-orange-500/20 transition-all duration-300">
                    {/* Header */}
                    <div className="mb-4">
                      <h3 className="text-xl font-bold text-white mb-1">{exp.title}</h3>
                      <h4 className="text-orange-500 font-semibold mb-2">{exp.company}</h4>
                      <div className="flex flex-col sm:flex-row sm:justify-between text-sm text-gray-400">
                        <span>{exp.period}</span>
                        <span>{exp.location}</span>
                      </div>
                      {exp.current && (
                        <span className="inline-block mt-2 px-3 py-1 bg-orange-500 text-white text-xs rounded-full">
                          Current
                        </span>
                      )}
                    </div>

                    {/* Description */}
                    <p className="text-gray-300 mb-4 leading-relaxed">
                      {exp.description}
                    </p>

                    {/* Projects */}
                    {exp.projects && (
                      <div>
                        <h5 className="text-white font-semibold mb-2">Key Projects:</h5>
                        <ul className="space-y-1">
                          {exp.projects.map((project, projectIndex) => (
                            <li key={projectIndex} className="text-gray-400 text-sm">
                              • {project}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Experience;
