'use client';

import { FaFacebook, FaInstagram, FaGithub, FaLinkedin, FaWhatsapp, FaTelegram } from 'react-icons/fa';
import { FaUser, FaGraduationCap, FaCogs, FaBriefcase, FaProjectDiagram, FaServicestack, FaEnvelope } from 'react-icons/fa';
import { useState, useEffect, useRef } from 'react';
import { gsap } from 'gsap';

const Header = () => {
  const [displayText, setDisplayText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const menuRef = useRef(null);
  const menuItemsRef = useRef([]);

  const texts = [' NextJs Developer,ReactJs Developer', 'Web Developer','Ui Developer'];

  const menuItems = [
    { icon: FaUser, label: 'About' },
    { icon: FaGraduationCap, label: 'Education' },
    { icon: FaCogs, label: 'Skills' },
    { icon: FaBriefcase, label: 'Experience' },
    { icon: FaProjectDiagram, label: 'Projects' },
    { icon: FaServicestack, label: 'Services' },
    { icon: FaEnvelope, label: 'Contact' }
  ];

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);

    if (!isMenuOpen) {
      // Opening animation
      gsap.set(menuRef.current, { display: 'flex' });
      gsap.fromTo(menuRef.current,
        { x: '100%', opacity: 0 },
        { x: '0%', opacity: 1, duration: 0.5, ease: 'power2.out' }
      );

      // Animate menu items one by one
      gsap.fromTo(menuItemsRef.current,
        { x: 50, opacity: 0 },
        {
          x: 0,
          opacity: 1,
          duration: 0.3,
          stagger: 0.1,
          delay: 0.2,
          ease: 'power2.out'
        }
      );
    } else {
      // Closing animation
      gsap.to(menuItemsRef.current, {
        x: 50,
        opacity: 0,
        duration: 0.2,
        stagger: 0.05
      });

      gsap.to(menuRef.current, {
        x: '100%',
        opacity: 0,
        duration: 0.4,
        delay: 0.3,
        ease: 'power2.in',
        onComplete: () => {
          gsap.set(menuRef.current, { display: 'none' });
        }
      });
    }
  };

  useEffect(() => {
    const currentText = texts[currentIndex];
    const timeout = setTimeout(() => {
      if (!isDeleting) {
        // Typing
        if (displayText.length < currentText.length) {
          setDisplayText(currentText.slice(0, displayText.length + 1));
        } else {
          // Pause before deleting
          setTimeout(() => setIsDeleting(true), 2000);
        }
      } else {
        // Deleting
        if (displayText.length > 0) {
          setDisplayText(displayText.slice(0, -1));
        } else {
          setIsDeleting(false);
          setCurrentIndex((prev) => (prev + 1) % texts.length);
        }
      }
    }, isDeleting ? 50 : 100);

    return () => clearTimeout(timeout);
  }, [displayText, currentIndex, isDeleting, texts]);

  return (
    <header className="min-h-screen flex">
      {/* Left Side - Orange Section */}
      <div className="w-1/2 bg-gradient-to-br from-orange-500 to-red-500 flex flex-col justify-between p-8 relative">
        {/* Top Section with Name */}
        <div className="flex items-center text-white">
          <span className="text-2xl font-bold">&lt;/&gt; Moaz Raheem</span>
        </div>

        {/* Center Section with Profile Image */}
        <div className="absolute inset-0 flex justify-center items-center">
          <div className="relative">
            <div className="w-80 h-80 rounded-full border-8 border-red-600 shadow-2xl bg-gradient-to-br from-red-600 to-orange-600 flex items-center justify-center">
              {/* Placeholder for profile image - replace with actual image */}
              <div className="w-72 h-72 rounded-full bg-gray-800 flex items-center justify-center text-white text-6xl font-bold">
                MR
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section with Social Icons */}
        <div className="flex gap-4 justify-start z-10">
          <a
            href="#"
            className="w-12 h-12 rounded-full flex items-center justify-center text-white hover:opacity-80 transition-colors"
            style={{backgroundColor: 'rgb(16, 19, 20)'}}
          >
            <FaFacebook size={20} />
          </a>
          <a
            href="#"
            className="w-12 h-12 rounded-full flex items-center justify-center text-white hover:opacity-80 transition-colors"
            style={{backgroundColor: 'rgb(16, 19, 20)'}}
          >
            <FaInstagram size={20} />
          </a>
          <a
            href="#"
            className="w-12 h-12 rounded-full flex items-center justify-center text-white hover:opacity-80 transition-colors"
            style={{backgroundColor: 'rgb(16, 19, 20)'}}
          >
            <FaGithub size={20} />
          </a>
          <a
            href="#"
            className="w-12 h-12 rounded-full flex items-center justify-center text-white hover:opacity-80 transition-colors"
            style={{backgroundColor: 'rgb(16, 19, 20)'}}
          >
            <FaLinkedin size={20} />
          </a>
          <a
            href="#"
            className="w-12 h-12 rounded-full flex items-center justify-center text-white hover:opacity-80 transition-colors"
            style={{backgroundColor: 'rgb(16, 19, 20)'}}
          >
            <FaWhatsapp size={20} />
          </a>
          <a
            href="#"
            className="w-12 h-12 rounded-full flex items-center justify-center text-white hover:opacity-80 transition-colors"
            style={{backgroundColor: 'rgb(16, 19, 20)'}}
          >
            <FaTelegram size={20} />
          </a>
        </div>
      </div>

      {/* Right Side - Dark Section */}
      <div className="w-1/2 flex flex-col justify-center p-16 text-white" style={{backgroundColor: 'rgb(16, 19, 20)'}}>
        {/* Greeting */}
        <p className="text-lg text-gray-300 mb-4">Hi There!</p>

        {/* Main Title */}
        <h1 className="text-5xl font-bold mb-6">
          <span className="text-white">I'M </span>
          <span className="text-orange-500">Moaz Raheem</span>
        </h1>

        {/* Animated Subtitle */}
        <div className="text-xl text-gray-300 mb-12 h-8">
          <span>{displayText}</span>
          <span className="animate-pulse text-orange-500">|</span>
        </div>
        
        {/* Buttons */}
        <div className="flex gap-6">
          <button className="px-8 py-3 border-2 border-orange-500 text-orange-500 rounded-full hover:bg-orange-500 hover:text-white transition-colors font-medium">
            Download CV
          </button>
          <button className="px-8 py-3 bg-orange-500 text-white rounded-full hover:bg-orange-600 transition-colors font-medium">
            Contact
          </button>
        </div>
      </div>

      {/* Burger Menu Button */}
      <button
        onClick={toggleMenu}
        className="fixed top-6 right-6 z-50 w-12 h-12 rounded-md flex flex-col justify-center items-center gap-1 transition-all duration-300 hover:scale-110"
        style={{backgroundColor: 'rgb(16, 19, 20)'}}
      >
        <span className={`w-6 h-0.5 bg-white transition-all duration-300 ${isMenuOpen ? 'rotate-45 translate-y-2' : ''}`}></span>
        <span className={`w-6 h-0.5 bg-white transition-all duration-300 ${isMenuOpen ? 'opacity-0' : ''}`}></span>
        <span className={`w-6 h-0.5 bg-white transition-all duration-300 ${isMenuOpen ? '-rotate-45 -translate-y-2' : ''}`}></span>
      </button>

      {/* Side Navigation Menu */}
      <div
        ref={menuRef}
        className="fixed top-0 right-0 h-full w-80 z-40 flex-col justify-center items-start p-8 hidden"
        style={{backgroundColor: 'rgb(16, 19, 20)'}}
      >
        <div className="flex flex-col gap-6 w-full">
          {menuItems.map((item, index) => {
            const IconComponent = item.icon;
            return (
              <button
                key={item.label}
                ref={el => menuItemsRef.current[index] = el}
                className="flex items-center gap-4 text-white text-lg font-medium hover:text-orange-500 transition-colors duration-300 p-3 rounded-lg hover:bg-white/5 w-full text-left"
              >
                <IconComponent size={20} />
                <span>{item.label}</span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Overlay */}
      {isMenuOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-30"
          onClick={toggleMenu}
        />
      )}
    </header>
  );
};

export default Header;
