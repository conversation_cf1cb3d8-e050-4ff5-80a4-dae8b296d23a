'use client';

import React from 'react';
import Slider from 'react-slick';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import {
  SiJavascript,
  SiReact,
  SiNodedotjs,
  SiExpress,
  SiMongodb,
  SiTypescript,
  SiNextdotjs,
  SiPython
} from 'react-icons/si';

const Skills = () => {
  const settings = {
    dots: false,
    infinite: true,
    speed: 500,
    slidesToShow: 4,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 2000,
    pauseOnHover: true,
    arrows: true,
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 1,
        }
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1,
        }
      },
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        }
      }
    ]
  };

  const skillsData = [
    {
      name: "JavaScript",
      icon: SiJavascript,
      description: "Modern ES6+ JavaScript"
    },
    {
      name: "React",
      icon: SiReact,
      description: "Frontend Library"
    },
    {
      name: "Node.js",
      icon: SiNodedotjs,
      description: "Backend Runtime"
    },
    {
      name: "Express",
      icon: SiExpress,
      description: "Web Framework"
    },
    {
      name: "MongoDB",
      icon: SiMongodb,
      description: "NoSQL Database"
    },
    {
      name: "TypeScript",
      icon: SiTypescript,
      description: "Typed JavaScript"
    },
    {
      name: "Next.js",
      icon: SiNextdotjs,
      description: "React Framework"
    },
    {
      name: "Python",
      icon: SiPython,
      description: "Programming Language"
    }
  ];



  return (
    <section className="min-h-screen flex flex-col justify-center py-16" style={{backgroundColor: 'rgb(16, 19, 20)'}}>
      <div className="container mx-auto px-4 md:px-8 lg:px-16">
        {/* Title */}
        <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-center mb-12 md:mb-16 text-orange-500">
          Skills
        </h2>

        {/* Skills Grid */}
        <div className="max-w-4xl mx-auto">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-16 justify-items-center ">
            {skillsData.map((skill, index) => (
              <div
                key={index}
                className="skill-box hover:scale-105 transition-transform duration-300"
                style={{
                  backgroundColor: 'rgb(16, 19, 20)',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: '150px',
                  height: '150px',
                  borderRadius: '10px',
                  // boxShadow: '0 0 20px #e96437, 0 0 40px #e96437, 0 0 60px #e96437, 0 0 80px #e96437',
                  border: '2px solid #e96437'
                }}
              >
                {/* Icon */}
                <div className="text-center">
                  <skill.icon className="text-white text-3xl mb-2 mx-auto" />

                  {/* Skill Name */}
                  <h3 className="text-white font-semibold text-sm">
                    {skill.name}
                  </h3>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Custom Styles */}
      <style jsx global>{`
        .skill-box {
          position: relative;
          background-color: rgb(16, 19, 20) !important;
          border: 2px solid #e96437 !important;
          box-shadow:
            0 0 10px #e96437,
            0 0 20px #e96437,
            0 0 30px #e96437,
            0 0 40px #e96437,
            inset 0 0 10px rgba(233, 100, 55, 0.1) !important;
        }

        .skill-box:hover {
          box-shadow:
            0 0 15px #e96437,
            0 0 25px #e96437,
            0 0 35px #e96437,
            0 0 50px #e96437,
            inset 0 0 15px rgba(233, 100, 55, 0.2) !important;
        }
      `}</style>
    </section>
  );
};

export default Skills;
