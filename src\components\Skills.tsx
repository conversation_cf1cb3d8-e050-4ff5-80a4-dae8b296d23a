'use client';

import React from 'react';
import {
  SiJavascript,
  SiReact,
  SiNodedotjs,
  SiExpress,
  SiMongodb,
  SiTypescript,
  SiNextdotjs,
  SiPython
} from 'react-icons/si';

const Skills = () => {
  const skillsData = [
    { name: 'React', icon: SiReact, percentage: 90 },
    { name: 'JavaScript', icon: SiJavascript, percentage: 85 },
    { name: 'Next.js', icon: SiNextdotjs, percentage: 88 },
    { name: 'TypeScript', icon: SiTypescript, percentage: 80 },
    { name: 'Node.js', icon: SiNodedotjs, percentage: 75 },
    { name: 'Express', icon: SiExpress, percentage: 70 },
    { name: 'MongoDB', icon: SiMongodb, percentage: 72 },
    { name: 'Python', icon: SiPython, percentage: 65 }
  ];



  return (
    <section className="min-h-screen flex flex-col justify-center py-16" style={{backgroundColor: 'rgb(16, 19, 20)'}}>
      <div className="container mx-auto px-4 md:px-8 lg:px-16">
        {/* Title */}
        <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-center mb-12 md:mb-16 text-orange-500">
          Skills
        </h2>

        {/* Skills Grid */}
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8 justify-items-center">
            {skillsData.map((skill, index) => (
              <div key={index} className="flex flex-col items-center">
                {/* Circular Progress */}
                <div className="relative w-32 h-32 mb-4">
                  {/* Background Circle */}
                  <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 120 120">
                    <circle
                      cx="60"
                      cy="60"
                      r="50"
                      stroke="rgba(233, 100, 55, 0.2)"
                      strokeWidth="8"
                      fill="transparent"
                    />
                    {/* Progress Circle */}
                    <circle
                      cx="60"
                      cy="60"
                      r="50"
                      stroke="#e96437"
                      strokeWidth="8"
                      fill="transparent"
                      strokeDasharray={`${2 * Math.PI * 50}`}
                      strokeDashoffset={`${2 * Math.PI * 50 * (1 - skill.percentage / 100)}`}
                      strokeLinecap="round"
                      className="transition-all duration-1000 ease-out"
                    />
                  </svg>

                  {/* Icon and Percentage */}
                  <div className="absolute inset-0 flex flex-col items-center justify-center">
                    <skill.icon className="text-white text-2xl mb-1" />
                    <span className="text-orange-500 font-bold text-sm">{skill.percentage}%</span>
                  </div>
                </div>

                {/* Skill Name */}
                <h3 className="text-white font-semibold text-center">
                  {skill.name}
                </h3>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Custom Styles */}
      <style jsx global>{`
        .skill-box {
          position: relative;
          background-color: rgb(16, 19, 20) !important;
          border: 2px solid #e96437 !important;
          box-shadow:
            0 0 10px #e96437,
            0 0 20px #e96437,
            0 0 30px #e96437,
            0 0 40px #e96437,
            inset 0 0 10px rgba(233, 100, 55, 0.1) !important;
        }

        .skill-box:hover {
          box-shadow:
            0 0 15px #e96437,
            0 0 25px #e96437,
            0 0 35px #e96437,
            0 0 50px #e96437,
            inset 0 0 15px rgba(233, 100, 55, 0.2) !important;
        }
      `}</style>
    </section>
  );
};

export default Skills;
