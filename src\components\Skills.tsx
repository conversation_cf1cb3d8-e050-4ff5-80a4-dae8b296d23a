'use client';

import React, { useState, useEffect, useRef } from 'react';
import {
  SiJavascript,
  SiReact,
  SiNodedotjs,
  SiExpress,
  SiMongodb,
  SiTypescript,
  SiNextdotjs,
  SiPython
} from 'react-icons/si';

const Skills = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [animatedPercentages, setAnimatedPercentages] = useState<number[]>([]);
  const sectionRef = useRef<HTMLElement>(null);

  const skillsData = [
    { name: 'React', icon: SiReact, percentage: 90 },
    { name: 'JavaScript', icon: SiJavascript, percentage: 85 },
    { name: 'Next.js', icon: SiNextdotjs, percentage: 88 },
    { name: 'TypeScript', icon: SiTypescript, percentage: 80 },
    { name: 'Node.js', icon: SiNodedotjs, percentage: 75 },
    { name: 'Express', icon: SiExpress, percentage: 70 },
    { name: 'MongoDB', icon: <PERSON>Mongodb, percentage: 72 },
    { name: 'Python', icon: SiPython, percentage: 65 }
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true);
          // Animate percentages
          skillsData.forEach((skill, index) => {
            setTimeout(() => {
              let currentPercentage = 0;
              const increment = skill.percentage / 50; // 50 steps for smooth animation
              const timer = setInterval(() => {
                currentPercentage += increment;
                if (currentPercentage >= skill.percentage) {
                  currentPercentage = skill.percentage;
                  clearInterval(timer);
                }
                setAnimatedPercentages(prev => {
                  const newPercentages = [...prev];
                  newPercentages[index] = Math.round(currentPercentage);
                  return newPercentages;
                });
              }, 20);
            }, index * 200); // Stagger animation
          });
        }
      },
      { threshold: 0.3 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, [isVisible]);

  // Initialize animated percentages
  useEffect(() => {
    setAnimatedPercentages(new Array(skillsData.length).fill(0));
  }, []);



  return (
    <section
      ref={sectionRef}
      className="min-h-screen flex flex-col justify-center py-20"
      style={{backgroundColor: 'rgb(16, 19, 20)'}}
    >
      <div className="container mx-auto px-4 md:px-8 lg:px-16">
        {/* Title */}
        <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-center mb-16 md:mb-20" style={{color: '#e96437'}}>
          Skills
        </h2>

        {/* Skills Grid */}
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-12 md:gap-16 justify-items-center">
            {skillsData.map((skill, index) => {
              const currentPercentage = animatedPercentages[index] || 0;
              const circumference = 2 * Math.PI * 60;
              const strokeDashoffset = circumference - (circumference * currentPercentage) / 100;

              return (
                <div key={index} className="flex flex-col items-center">
                  {/* Circular Progress */}
                  <div className="relative w-40 h-40 md:w-44 md:h-44 mb-6">
                    {/* Background Circle */}
                    <svg className="w-full h-full transform -rotate-90" viewBox="0 0 140 140">
                      <circle
                        cx="70"
                        cy="70"
                        r="60"
                        stroke="rgba(233, 100, 55, 0.1)"
                        strokeWidth="10"
                        fill="transparent"
                      />
                      {/* Progress Circle */}
                      <circle
                        cx="70"
                        cy="70"
                        r="60"
                        stroke="#e96437"
                        strokeWidth="10"
                        fill="transparent"
                        strokeDasharray={circumference}
                        strokeDashoffset={strokeDashoffset}
                        strokeLinecap="round"
                        className="transition-all duration-75 ease-out"
                        style={{
                          filter: 'drop-shadow(0 0 4px rgba(233, 100, 55, 0.6))'
                        }}
                      />
                    </svg>

                    {/* Icon and Percentage */}
                    <div className="absolute inset-0 flex flex-col items-center justify-center">
                      <skill.icon
                        className="text-3xl md:text-4xl mb-2"
                        style={{color: '#e96437'}}
                      />
                      <span
                        className="font-bold text-lg md:text-xl"
                        style={{color: '#e96437'}}
                      >
                        {currentPercentage}%
                      </span>
                    </div>
                  </div>

                  {/* Skill Name */}
                  <h3
                    className="font-semibold text-center text-lg md:text-xl"
                    style={{color: '#e96437'}}
                  >
                    {skill.name}
                  </h3>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Custom Styles */}
      <style jsx global>{`
        .skill-box {
          position: relative;
          background-color: rgb(16, 19, 20) !important;
          border: 2px solid #e96437 !important;
          box-shadow:
            0 0 10px #e96437,
            0 0 20px #e96437,
            0 0 30px #e96437,
            0 0 40px #e96437,
            inset 0 0 10px rgba(233, 100, 55, 0.1) !important;
        }

        .skill-box:hover {
          box-shadow:
            0 0 15px #e96437,
            0 0 25px #e96437,
            0 0 35px #e96437,
            0 0 50px #e96437,
            inset 0 0 15px rgba(233, 100, 55, 0.2) !important;
        }
      `}</style>
    </section>
  );
};

export default Skills;
