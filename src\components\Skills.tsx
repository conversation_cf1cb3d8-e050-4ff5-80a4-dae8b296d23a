'use client';

import React from 'react';
import Slider from 'react-slick';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';

const Skills = () => {
  const skillsData = [
    {
      name: "JavaScript",
      icon: "JS",
      description: "Modern ES6+ JavaScript"
    },
    {
      name: "React",
      icon: "⚛️",
      description: "Frontend Library"
    },
    {
      name: "Node.js",
      icon: "JS",
      description: "Backend Runtime"
    },
    {
      name: "Express",
      icon: "EX",
      description: "Web Framework"
    },
    {
      name: "MongoDB",
      icon: "DB",
      description: "NoSQL Database"
    },
    {
      name: "TypeScript",
      icon: "TS",
      description: "Typed JavaScript"
    },
    {
      name: "Next.js",
      icon: "NX",
      description: "React Framework"
    },
    {
      name: "Python",
      icon: "PY",
      description: "Programming Language"
    }
  ];

  const settings = {
    dots: false,
    infinite: true,
    speed: 500,
    slidesToShow: 4,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 2000,
    pauseOnHover: true,
    arrows: true,
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 1,
        }
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1,
        }
      },
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        }
      }
    ]
  };

  return (
    <section className="min-h-screen flex flex-col justify-center py-16" style={{backgroundColor: 'rgb(16, 19, 20)'}}>
      <div className="container mx-auto px-4 md:px-8 lg:px-16">
        {/* Title */}
        <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-center mb-12 md:mb-16 text-orange-500">
          Skills
        </h2>

        {/* Skills Carousel */}
        <div className="skills-slider">
          <Slider {...settings}>
            {skillsData.map((skill, index) => (
              <div key={index} className="px-4">
                <div className="bg-black rounded-2xl p-8 mx-2 hover:scale-105 transition-transform duration-300 border-2 border-orange-500 relative overflow-hidden group">
                  {/* Glowing effect */}
                  <div className="absolute inset-0 bg-orange-500 opacity-20 blur-xl group-hover:opacity-30 transition-opacity duration-300"></div>
                  
                  {/* Content */}
                  <div className="relative z-10 text-center">
                    {/* Icon */}
                    <div className="w-16 h-16 mx-auto mb-4 bg-orange-500 rounded-lg flex items-center justify-center">
                      <span className="text-white font-bold text-xl">
                        {skill.icon}
                      </span>
                    </div>
                    
                    {/* Skill Name */}
                    <h3 className="text-white font-bold text-lg md:text-xl mb-2">
                      {skill.name}
                    </h3>
                    
                    {/* Description */}
                    <p className="text-gray-300 text-sm">
                      {skill.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </Slider>
        </div>
      </div>

      {/* Custom Styles */}
      <style jsx global>{`
        .skills-slider .slick-prev,
        .skills-slider .slick-next {
          z-index: 10;
          width: 40px;
          height: 40px;
        }
        
        .skills-slider .slick-prev {
          left: -50px;
        }
        
        .skills-slider .slick-next {
          right: -50px;
        }
        
        .skills-slider .slick-prev:before,
        .skills-slider .slick-next:before {
          font-size: 40px;
          color: #ff6b35;
        }
        
        .skills-slider .slick-track {
          display: flex;
          align-items: center;
        }
        
        .skills-slider .slick-slide {
          height: auto;
        }
        
        .skills-slider .slick-slide > div {
          height: 100%;
        }
        
        @media (max-width: 768px) {
          .skills-slider .slick-prev {
            left: -30px;
          }
          
          .skills-slider .slick-next {
            right: -30px;
          }
          
          .skills-slider .slick-prev:before,
          .skills-slider .slick-next:before {
            font-size: 30px;
          }
        }
      `}</style>
    </section>
  );
};

export default Skills;
