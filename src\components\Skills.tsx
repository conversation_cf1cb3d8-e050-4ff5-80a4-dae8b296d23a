'use client';

import React from 'react';
import Slider from 'react-slick';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import {
  SiJavascript,
  SiReact,
  SiNodedotjs,
  SiExpress,
  SiMongodb,
  SiTypescript,
  SiNextdotjs,
  SiPython
} from 'react-icons/si';

const Skills = () => {
  const skillsData = [
    {
      name: "JavaScript",
      icon: SiJavascript,
      description: "Modern ES6+ JavaScript"
    },
    {
      name: "React",
      icon: SiReact,
      description: "Frontend Library"
    },
    {
      name: "Node.js",
      icon: SiNodedotjs,
      description: "Backend Runtime"
    },
    {
      name: "Express",
      icon: SiExpress,
      description: "Web Framework"
    },
    {
      name: "MongoDB",
      icon: SiMongodb,
      description: "NoSQL Database"
    },
    {
      name: "TypeScript",
      icon: SiTypescript,
      description: "Typed JavaScript"
    },
    {
      name: "Next.js",
      icon: SiNextdotjs,
      description: "React Framework"
    },
    {
      name: "Python",
      icon: Si<PERSON><PERSON><PERSON>,
      description: "Programming Language"
    }
  ];

  const settings = {
    dots: false,
    infinite: true,
    speed: 500,
    slidesToShow: 4,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 2000,
    pauseOnHover: true,
    arrows: true,
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 1,
        }
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1,
        }
      },
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        }
      }
    ]
  };

  return (
    <section className="min-h-screen flex flex-col justify-center py-16" style={{backgroundColor: 'rgb(16, 19, 20)'}}>
      <div className="container mx-auto px-4 md:px-8 lg:px-16">
        {/* Title */}
        <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-center mb-12 md:mb-16 text-orange-500">
          Skills
        </h2>

        {/* Skills Carousel */}
        <div className="skills-slider">
          <Slider {...settings}>
            {skillsData.map((skill, index) => (
              <div key={index} className="px-4">
                <div
                  className="skill-box mx-2 hover:scale-105 transition-transform duration-300"
                  style={{
                    backgroundColor: 'rgb(16, 19, 20)',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '150px',
                    height: '150px',
                    borderRadius: '10px',
                    boxShadow: '0 0 20px #e96437, 0 0 40px #e96437, 0 0 60px #e96437, 0 0 80px #e96437',
                    border: '2px solid #e96437'
                  }}
                >
                  {/* Icon */}
                  <div className="text-center">
                    <skill.icon className="text-white text-3xl mb-2 mx-auto" />

                    {/* Skill Name */}
                    <h3 className="text-white font-semibold text-sm">
                      {skill.name}
                    </h3>
                  </div>
                </div>
              </div>
            ))}
          </Slider>
        </div>
      </div>

      {/* Custom Styles */}
      <style jsx global>{`
        .skill-box {
          position: relative;
          background-color: rgb(16, 19, 20) !important;
          border: 2px solid #e96437 !important;
          box-shadow:
            0 0 10px #e96437,
            0 0 20px #e96437,
            0 0 30px #e96437,
            0 0 40px #e96437,
            inset 0 0 10px rgba(233, 100, 55, 0.1) !important;
        }

        .skill-box:hover {
          box-shadow:
            0 0 15px #e96437,
            0 0 25px #e96437,
            0 0 35px #e96437,
            0 0 50px #e96437,
            inset 0 0 15px rgba(233, 100, 55, 0.2) !important;
        }

        .skills-slider .slick-prev,
        .skills-slider .slick-next {
          z-index: 10;
          width: 40px;
          height: 40px;
        }

        .skills-slider .slick-prev {
          left: -50px;
        }

        .skills-slider .slick-next {
          right: -50px;
        }

        .skills-slider .slick-prev:before,
        .skills-slider .slick-next:before {
          font-size: 40px;
          color: #e96437;
        }

        .skills-slider .slick-track {
          display: flex;
          align-items: center;
        }

        .skills-slider .slick-slide {
          height: auto;
        }

        .skills-slider .slick-slide > div {
          height: 100%;
        }

        @media (max-width: 768px) {
          .skills-slider .slick-prev {
            left: -30px;
          }

          .skills-slider .slick-next {
            right: -30px;
          }

          .skills-slider .slick-prev:before,
          .skills-slider .slick-next:before {
            font-size: 30px;
          }
        }
      `}</style>
    </section>
  );
};

export default Skills;
