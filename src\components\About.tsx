'use client';

import React from 'react';

const About = () => {
  return (
    <section className="min-h-screen flex flex-col lg:flex-row items-center" style={{backgroundColor: 'rgb(16, 19, 20)'}}>
      {/* Left Side - Text Content */}
      <div className="w-full lg:w-1/2 flex flex-col justify-center p-4 md:p-8 lg:p-16 text-white">
        {/* Main Title */}
        <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 md:mb-8 text-white">
          LET ME INTRODUCE MYSELF
        </h2>

        {/* Description Text */}
        <div className="text-base md:text-lg lg:text-xl text-gray-300 leading-relaxed space-y-4">
          <p>
            I fell in love with programming and have learned a lot along the way—though I still have much to explore! I am fluent in technologies like{' '}
            <span className="text-orange-500 font-semibold">React.js</span>,{' '}
            <span className="text-orange-500 font-semibold">Android Development</span>,{' '}
            <span className="text-orange-500 font-semibold">Javascript</span>, and full-stack development with the{' '}
            <span className="text-orange-500 font-semibold">MERN</span> stack (MongoDB, Express.js, React.js, Node.js).
          </p>
          
          <p>
            My primary interest lies in building dynamic{' '}
            <span className="text-orange-500 font-semibold">React applications</span> and developing robust{' '}
            <span className="text-orange-500 font-semibold">Android apps</span>. Whenever possible, I also apply my passion for creating polished portfolios and responsive web applications, such as{' '}
            <span className="text-orange-500 font-semibold">Textutils</span>.
          </p>
        </div>
      </div>

      {/* Right Side - Illustration */}
      <div className="w-full lg:w-1/2 flex justify-center items-center p-4 md:p-8 lg:p-16">
        <div className="relative">
          {/* Developer Illustration */}
          <div className="w-64 h-64 md:w-80 md:h-80 lg:w-96 lg:h-96 relative">
            {/* Hoodie/Person Shape */}
            <div className="absolute inset-0 flex items-center justify-center">
              <svg 
                viewBox="0 0 400 400" 
                className="w-full h-full text-white"
                fill="currentColor"
              >
                {/* Hood */}
                <path d="M200 50 C120 50, 80 100, 80 160 L80 200 C80 220, 90 240, 110 250 L110 300 C110 320, 130 340, 150 340 L250 340 C270 340, 290 320, 290 300 L290 250 C310 240, 320 220, 320 200 L320 160 C320 100, 280 50, 200 50 Z" />
                
                {/* Face area (darker) */}
                <ellipse cx="200" cy="140" rx="60" ry="50" fill="rgb(16, 19, 20)" />
                
                {/* Eyes */}
                <circle cx="180" cy="130" r="3" fill="white" />
                <circle cx="220" cy="130" r="3" fill="white" />
                
                {/* Smile */}
                <path d="M170 150 Q200 170 230 150" stroke="white" strokeWidth="2" fill="none" />
                
                {/* Laptop */}
                <rect x="140" y="220" width="120" height="80" rx="8" fill="rgb(60, 60, 60)" />
                <rect x="145" y="225" width="110" height="60" rx="4" fill="rgb(20, 20, 20)" />
                
                {/* Code brackets on laptop screen */}
                <text x="200" y="260" textAnchor="middle" fill="#ff6b35" fontSize="24" fontFamily="monospace" fontWeight="bold">
                  &lt;/&gt;
                </text>
                
                {/* Keyboard */}
                <rect x="145" y="285" width="110" height="10" rx="2" fill="rgb(40, 40, 40)" />
                
                {/* Arms */}
                <ellipse cx="120" cy="240" rx="15" ry="40" fill="currentColor" />
                <ellipse cx="280" cy="240" rx="15" ry="40" fill="currentColor" />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
