'use client';

import React from 'react';

const About = () => {
  return (
    <section className="min-h-screen flex flex-col lg:flex-row items-center" style={{backgroundColor: 'rgb(16, 19, 20)'}}>
      {/* Left Side - Text Content */}
      <div className="w-full lg:w-1/2 flex flex-col justify-center p-4 md:p-8 lg:p-16 text-white">
        {/* Main Title */}
        <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 md:mb-8 text-white">
          LET ME INTRODUCE MYSELF
        </h2>

        {/* Description Text */}
        <div className="text-base md:text-lg lg:text-xl text-gray-300 leading-relaxed space-y-4">
          <p>
            I fell in love with programming and have learned a lot along the way—though I still have much to explore! I am fluent in technologies like{' '}
            <span className="text-orange-500 font-semibold">React.js</span>,{' '}
            <span className="text-orange-500 font-semibold">Android Development</span>,{' '}
            <span className="text-orange-500 font-semibold">Javascript</span>, and full-stack development with the{' '}
            <span className="text-orange-500 font-semibold">MERN</span> stack (MongoDB, Express.js, React.js, Node.js).
          </p>
          
          <p>
            My primary interest lies in building dynamic{' '}
            <span className="text-orange-500 font-semibold">React applications</span> and developing robust{' '}
            <span className="text-orange-500 font-semibold">Android apps</span>. Whenever possible, I also apply my passion for creating polished portfolios and responsive web applications, such as{' '}
            <span className="text-orange-500 font-semibold">Textutils</span>.
          </p>
        </div>
      </div>

      {/* Right Side - Image */}
      <div className="w-full lg:w-1/2 flex justify-center items-center p-4 md:p-8 lg:p-16">
        <div className="relative">
          <div className="w-64 h-64 md:w-80 md:h-80 lg:w-96 lg:h-96 relative">
            <img
              src="/developer-illustration.png"
              alt="Developer working on laptop"
              className="w-full h-full object-contain"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
